defmodule MqttableWeb.ConnectionsLive do
  @moduledoc """
  Main LiveView module for managing MQTT connections and broker interactions.

  This module serves as the main coordinator for various MQTT operations,
  delegating specific functionality to specialized manager modules:
  - ConnectionManager: MQTT connection operations
  - SubscriptionManager: Topic subscription management
  - MessageHandler: Message sending and tracing
  - ModalStateManager: Modal state management
  - FileUploadHandler: File upload operations
  - ScheduledMessageManager: Scheduled message operations

  The module follows functional programming principles with pattern matching,
  pure functions where possible, and proper error handling with tagged tuples.
  """

  use MqttableWeb, :live_view
  require Logger

  import Phoenix.LiveView,
    only: [
      put_flash: 3,
      stream: 3,
      connected?: 1,
      send_update: 2,
      cancel_upload: 3,
      redirect: 2
    ]

  import Phoenix.Component, only: [assign: 2, assign: 3, live_component: 1]

  # Core dependencies
  alias Mqttable.ConnectionSets
  alias Mqttable.Settings
  alias Mqttable.MqttClient.Manager, as: MqttClientManager
  alias MqttableWeb.ConnectionSets.Manager, as: ConnectionSetsManager
  alias MqttableWeb.Connections.Manager, as: ConnectionsManager
  alias MqttableWeb.Variables.Manager, as: VariablesManager
  alias MqttableWeb.UI.StateManager
  alias MqttableWeb.Utils.ConnectionHelpers

  # Specialized manager modules
  alias MqttableWeb.ConnectionsLive.ConnectionManager
  alias MqttableWeb.ConnectionsLive.SubscriptionManager
  alias MqttableWeb.ConnectionsLive.MessageHandler
  alias MqttableWeb.ConnectionsLive.ModalStateManager
  alias MqttableWeb.ConnectionsLive.FileUploadHandler
  alias MqttableWeb.ConnectionsLive.ScheduledMessageManager

  # Type definitions
  @type socket :: Phoenix.LiveView.Socket.t()
  @type connection_set :: map()
  @type connection :: map()
  @type connection_sets :: [connection_set()]
  @type ui_state :: map()
  @type mqtt_result :: {:ok, any()} | {:error, atom(), String.t()}

  @impl true
  @spec mount(map(), map(), socket()) :: {:ok, socket()}
  def mount(_params, _session, socket) do
    socket =
      socket
      |> initialize_subscriptions()
      |> initialize_modal_assigns()

    {connection_sets, _ui_state, expanded_sets, active_connection_set} = load_ui_state_data()
    default_connection = create_default_connection()
    available_colors = create_available_colors()

    socket =
      socket
      |> setup_trace_and_form_state(active_connection_set)
      |> finalize_socket_setup(
        connection_sets,
        active_connection_set,
        default_connection,
        available_colors,
        expanded_sets
      )

    {:ok, socket}
  end

  # Private functions for mount initialization

  @spec initialize_subscriptions(socket()) :: socket()
  defp initialize_subscriptions(socket) do
    if connected?(socket) do
      ConnectionSets.subscribe()
      Settings.subscribe()
      Phoenix.PubSub.subscribe(Mqttable.PubSub, "mqtt_clients")
      Mqttable.MqttTelemetryTracing.subscribe()

      # Request current connection states to catch any missed connection time broadcasts
      send(self(), :sync_connection_states)
    end

    socket
  end

  @spec initialize_modal_assigns(socket()) :: socket()
  defp initialize_modal_assigns(socket) do
    socket
    |> assign(:show_subscription_modal, false)
    |> assign(:pre_selected_client_id, nil)
    |> assign(:edit_mode, false)
    |> assign(:client_id, nil)
    |> assign(:topic, nil)
    |> assign(:qos, 0)
    |> assign(:nl, false)
    |> assign(:rap, false)
    |> assign(:rh, 0)
    |> assign(:sub_id, nil)
    |> assign(:index, nil)
  end

  @spec create_available_colors() :: [{String.t(), String.t()}]
  defp create_available_colors do
    [
      {"blue", "bg-blue-500"},
      {"green", "bg-green-500"},
      {"red", "bg-red-500"},
      {"yellow", "bg-yellow-500"},
      {"purple", "bg-purple-500"}
    ]
  end

  @spec create_default_connection() :: connection()
  defp create_default_connection do
    %{
      name: ConnectionHelpers.generate_random_connection_name(),
      client_id: "mqtt_" <> ConnectionHelpers.generate_random_string(8),
      username: "",
      password: "",
      mqtt_version: "5.0",
      connect_timeout: 45,
      keep_alive: 300,
      clean_start: true,
      session_expiry_interval: 0,
      receive_maximum: nil,
      maximum_packet_size: nil,
      topic_alias_maximum: nil,
      request_response_info: false,
      request_problem_info: false,
      user_properties: [%{key: "", value: ""}],
      will_topic: "",
      will_qos: "0",
      will_retain: false,
      will_payload: "",
      will_payload_format: false,
      will_delay_interval: 0,
      will_message_expiry: 0,
      will_content_type: "",
      will_response_topic: "",
      will_correlation_data: "",
      topics: [],
      status: "disconnected",
      connection_time: nil
    }
  end

  @spec load_ui_state_data() :: {connection_sets(), ui_state(), map(), connection_set() | nil}
  defp load_ui_state_data do
    connection_sets = ConnectionSets.get_all()
    ui_state = ConnectionSets.get_ui_state()
    expanded_sets = Map.get(ui_state, :expanded_sets, %{})
    active_set_name = Map.get(ui_state, :active_connection_set)

    active_connection_set =
      if active_set_name do
        ConnectionHelpers.find_connection_set_by_name(connection_sets, active_set_name)
      else
        nil
      end

    {connection_sets, ui_state, expanded_sets, active_connection_set}
  end

  @spec setup_trace_and_form_state(socket(), connection_set() | nil) :: socket()
  defp setup_trace_and_form_state(socket, active_connection_set) do
    trace_messages =
      if active_connection_set && active_connection_set.name do
        Mqttable.TraceManager.get_messages(active_connection_set.name)
      else
        []
      end

    send_modal_form_state = load_send_modal_form_state(active_connection_set)

    socket
    |> stream(:trace_messages, trace_messages)
    |> assign(:send_modal_form_state, send_modal_form_state)
  end

  @spec finalize_socket_setup(
          socket(),
          connection_sets(),
          connection_set() | nil,
          connection(),
          [{String.t(), String.t()}],
          map()
        ) :: socket()
  defp finalize_socket_setup(
         socket,
         connection_sets,
         active_connection_set,
         default_connection,
         available_colors,
         expanded_sets
       ) do
    socket
    |> assign(:connection_sets, connection_sets)
    |> assign(:active_connection_set, active_connection_set)
    |> assign(:show_modal, false)
    |> assign(:modal_type, nil)
    |> assign(:edit_var, nil)
    |> assign(:edit_connection_set, nil)
    |> assign(:edit_connection, default_connection)
    |> assign(:available_colors, available_colors)
    |> assign(:uploaded_files, [])
    |> assign(:expanded_sets, expanded_sets)
    |> assign(:trace_active, false)
    |> assign(:show_send_modal, false)
    |> assign(:show_detail_modal, false)
    |> assign(:detail_modal_message, nil)
    |> assign(:payload_view_type, "plaintext")
    |> assign(:show_settings_modal, false)
    |> assign(:show_scheduled_message_modal, false)
    |> assign(:scheduled_message_edit_mode, false)
    |> assign(:scheduled_message_edit_index, nil)
    |> assign(:scheduled_message_data, nil)
    |> assign(:show_template_manager, false)
    |> assign_page_title(active_connection_set)
  end

  @impl true
  def handle_params(_params, _uri, socket) do
    # Since we no longer use URL parameters to track the active broker,
    # we just return the socket without making any changes
    {:noreply, socket}
  end

  # Common helper functions used throughout the module

  # Event handlers - delegated to specialized manager modules

  # Connection management events
  @impl true
  def handle_event(
        "update_connection_status",
        %{"client_id" => client_id, "new_status" => new_status},
        socket
      ) do
    ConnectionManager.handle_update_connection_status(socket, client_id, new_status)
  end

  # Subscription management events
  @impl true
  def handle_event("edit_subscription", params, socket) do
    SubscriptionManager.handle_edit_subscription(socket, params)
  end

  @impl true
  def handle_event("unsubscribe_topic", %{"client_id" => client_id, "topic" => topic}, socket) do
    SubscriptionManager.handle_unsubscribe_topic(socket, client_id, topic)
  end

  # Message handling events
  @impl true
  def handle_event("open_send_modal", _params, socket) do
    MessageHandler.handle_open_send_modal(socket)
  end

  @impl true
  def handle_event("close_send_modal", _params, socket) do
    MessageHandler.handle_close_send_modal(socket)
  end

  @impl true
  def handle_event("send_message_direct", _params, socket) do
    MessageHandler.handle_send_message_direct(socket)
  end

  @impl true
  def handle_event("open_detail_modal", %{"message" => message}, socket) do
    MessageHandler.handle_open_detail_modal(socket, message)
  end

  @impl true
  def handle_event("close_detail_modal", _params, socket) do
    MessageHandler.handle_close_detail_modal(socket)
  end

  @impl true
  def handle_event("switch_payload_view", %{"type" => type}, socket) do
    MessageHandler.handle_switch_payload_view(socket, type)
  end

  @impl true
  def handle_event("copy_payload", %{"payload" => payload}, socket) do
    MessageHandler.handle_copy_payload(socket, payload)
  end

  # Modal state management events
  @impl true
  def handle_event("open_subscription_modal", %{"set_name" => set_name}, socket) do
    ModalStateManager.handle_open_subscription_modal(socket, set_name)
  end

  @impl true
  def handle_event(
        "open_subscription_modal_for_client",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    ModalStateManager.handle_open_subscription_modal_for_client(socket, set_name, client_id)
  end

  @impl true
  def handle_event("close_subscription_modal", _params, socket) do
    ModalStateManager.handle_close_subscription_modal(socket)
  end

  @impl true
  def handle_event("open_settings_modal", _params, socket) do
    ModalStateManager.handle_open_settings_modal(socket)
  end

  @impl true
  def handle_event("close_settings_modal", _params, socket) do
    ModalStateManager.handle_close_settings_modal(socket)
  end

  # File upload events
  @impl true
  def handle_event(
        "file_uploaded",
        %{"filename" => filename, "content" => content, "size" => size, "type" => type},
        socket
      ) do
    FileUploadHandler.handle_file_uploaded(socket, filename, content, size, type)
  end

  @impl true
  def handle_event("cancel_upload", %{"upload_name" => upload_name, "ref" => ref}, socket) do
    FileUploadHandler.handle_cancel_upload(socket, upload_name, ref)
  end

  # Scheduled message events
  @impl true
  def handle_event(
        "remove_scheduled_message",
        %{"client_id" => client_id, "set_name" => set_name, "index" => index},
        socket
      ) do
    ScheduledMessageManager.handle_remove_scheduled_message(socket, set_name, client_id, index)
  end

  @impl true
  def handle_event(
        "edit_scheduled_message",
        %{"client_id" => client_id, "index" => index},
        socket
      ) do
    ModalStateManager.handle_edit_scheduled_message(socket, client_id, index)
  end

  # Legacy/remaining events that need to be handled directly
  @impl true
  def handle_event("open_connection_set_modal", %{"type" => "new_connection_set"}, socket) do
    ConnectionSetsManager.handle_open_connection_set_modal(socket)
  end

  @impl true
  def handle_event("open_edit_connection_set_modal", %{"name" => name}, socket) do
    ConnectionSetsManager.handle_open_edit_connection_set_modal(socket, %{"name" => name})
  end

  @impl true
  def handle_event("open_connections_modal", _params, socket) do
    StateManager.handle_open_connections_modal(socket)
  end

  @impl true
  def handle_event("select_broker_tab", %{"name" => name}, socket) do
    # Find the connection set by name
    set = ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, name)

    if set do
      # Check if this broker is already active to avoid unnecessary reloading
      current_active_name =
        if socket.assigns.active_connection_set,
          do: socket.assigns.active_connection_set.name,
          else: nil

      if current_active_name == name do
        # Same broker is already active, no need to reload
        {:noreply, socket}
      else
        # Mark this change as initiated locally to prevent loops
        socket = assign(socket, :active_set_change_source, "local")

        # Update the active connection set in the UI state to broadcast to all clients
        ConnectionSets.update_active_connection_set(name)

        # Load trace messages for this broker
        trace_messages = Mqttable.TraceManager.get_messages(name)

        # Load send modal form state for this broker
        send_modal_form_state = load_send_modal_form_state(set)

        {:noreply,
         socket
         |> assign(:active_connection_set, set)
         |> stream(:trace_messages, trace_messages)
         |> assign(:send_modal_form_state, send_modal_form_state)
         |> assign_page_title(set)}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("close_broker_tab", %{"name" => name}, socket) do
    handle_close_broker_tab(socket, name)
  end

  @impl true
  def handle_event(
        "reorder_broker_tabs",
        %{"old_index" => old_index, "new_index" => new_index},
        socket
      ) do
    # Reorder the connection sets based on the new order
    connection_sets = socket.assigns.connection_sets

    # Move the item from old_index to new_index
    {item, remaining} = List.pop_at(connection_sets, old_index)
    reordered_sets = List.insert_at(remaining, new_index, item)

    # Update the connection sets
    ConnectionSets.update(reordered_sets)

    socket = assign(socket, :connection_sets, reordered_sets)
    {:noreply, socket}
  end

  @impl true
  def handle_event("open_new_connection_modal", %{"name" => set_name}, socket) do
    ConnectionsManager.handle_open_new_connection_modal(socket, %{"name" => set_name})
  end

  @impl true
  def handle_event(
        "open_edit_connection_modal",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    ConnectionsManager.handle_open_edit_connection_modal(
      socket,
      %{"set_name" => set_name, "client_id" => client_id}
    )
  end

  @impl true
  def handle_event("close_modal", _params, socket) do
    StateManager.handle_close_modal(socket)
  end

  @impl true
  def handle_event("generate_client_id", _params, socket) do
    ConnectionsManager.handle_generate_client_id(socket)
  end

  @impl true
  def handle_event("generate_connection_name", _params, socket) do
    ConnectionsManager.handle_generate_connection_name(socket)
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    ConnectionsManager.handle_add_user_property(socket)
  end

  @impl true
  def handle_event("remove_user_property", %{"index" => index}, socket) do
    ConnectionsManager.handle_remove_user_property(socket, %{"index" => index})
  end

  @impl true
  def handle_event("user_property_changed", params, socket) do
    ConnectionsManager.handle_user_property_changed(socket, params)
  end

  @impl true
  def handle_event("check_new_property", %{"value" => value}, socket) do
    ConnectionsManager.handle_check_new_property(socket, %{"value" => value})
  end

  @impl true
  def handle_event("save_connection", params, socket) do
    ConnectionsManager.handle_save_connection(socket, params)
  end

  @impl true
  def handle_event("save_and_connect_connection", params, socket) do
    ConnectionsManager.handle_save_and_connect_connection(socket, params)
  end

  @impl true
  def handle_event("update_connection", params, socket) do
    ConnectionsManager.handle_update_connection(socket, params)
  end

  @impl true
  def handle_event(
        "delete_connection",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    ConnectionsManager.handle_delete_connection(
      socket,
      %{"set_name" => set_name, "client_id" => client_id}
    )
  end

  @impl true
  def handle_event(
        "save_connection_set",
        %{"connection_set" => set_params} = params,
        socket
      ) do
    ConnectionSetsManager.handle_save_connection_set(socket, %{
      "connection_set" => set_params,
      "variable" => Map.get(params, "variable", %{})
    })
  end

  @impl true
  def handle_event(
        "update_connection_set",
        %{"connection_set" => set_params, "old_name" => old_name, "variable" => var_params},
        socket
      ) do
    ConnectionSetsManager.handle_update_connection_set(
      socket,
      %{
        "connection_set" => set_params,
        "old_name" => old_name,
        "variable" => var_params
      }
    )
  end

  @impl true
  def handle_event(
        "update_connection_set",
        %{"connection_set" => set_params, "old_name" => old_name},
        socket
      ) do
    ConnectionSetsManager.handle_update_connection_set(
      socket,
      %{
        "connection_set" => set_params,
        "old_name" => old_name
      }
    )
  end

  @impl true
  def handle_event("save_variable", %{"variable" => var_params}, socket) do
    VariablesManager.handle_save_variable(socket, %{"variable" => var_params})
  end

  @impl true
  def handle_event("update_variable", %{"variable" => var_params, "old_name" => old_name}, socket) do
    VariablesManager.handle_update_variable(socket, %{
      "variable" => var_params,
      "old_name" => old_name
    })
  end

  @impl true
  def handle_event("delete_variable", %{"name" => name}, socket) do
    VariablesManager.handle_delete_variable(socket, %{"name" => name})
  end

  @impl true
  def handle_event("delete_connection_set", %{"name" => name}, socket) do
    ConnectionSetsManager.handle_delete_connection_set(socket, %{"name" => name})
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel-upload", %{"upload" => upload_name, "ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, String.to_existing_atom(upload_name), ref)}
  end

  @impl true
  def handle_event("clear_variables", _params, socket) do
    VariablesManager.handle_clear_variables(socket)
  end

  @impl true
  def handle_event("add_variable_row", params, socket) do
    VariablesManager.handle_add_variable_row(socket, params)
  end

  @impl true
  def handle_event(
        "mqtt_version_changed",
        %{"connection" => %{"mqtt_version" => _mqtt_version}} = params,
        socket
      ) do
    ConnectionsManager.handle_mqtt_version_changed(socket, params)
  end

  @impl true
  def handle_event(
        "open_scheduled_message_modal_for_client",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    # Find the connection set by name
    active_set =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if active_set do
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_scheduled_message_modal, true)
        |> assign(:pre_selected_client_id, client_id)
        |> assign(:scheduled_message_edit_mode, false)
        |> assign(:scheduled_message_edit_index, nil)
        |> assign(:scheduled_message_data, nil)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("close_scheduled_message_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_scheduled_message_modal, false)
      |> assign(:pre_selected_client_id, nil)
      |> assign(:scheduled_message_edit_mode, false)
      |> assign(:scheduled_message_edit_index, nil)
      |> assign(:scheduled_message_data, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event("load_older_messages", _params, socket) do
    # This event should be handled by TraceGridComponent, but sometimes
    # it gets sent to the parent LiveView. We just ignore it here.
    {:noreply, socket}
  end

  @impl true
  def handle_event("load_newer_messages", _params, socket) do
    # This event should be handled by TraceGridComponent, but sometimes
    # it gets sent to the parent LiveView. We just ignore it here.
    {:noreply, socket}
  end

  @impl true
  def handle_info({:close_broker_tab, name}, socket) do
    # Handle the close broker tab message from the component
    handle_close_broker_tab(socket, name)
  end

  @impl true
  def handle_info({:open_edit_connection_set_modal, params}, socket) do
    # Call the existing handler with the params
    handle_event("open_edit_connection_set_modal", params, socket)
  end

  @impl true
  def handle_info({:focus_element, element_id}, socket) do
    StateManager.handle_focus_element(socket, element_id)
  end

  @impl true
  def handle_info({:close_settings_modal}, socket) do
    {:noreply, assign(socket, :show_settings_modal, false)}
  end

  @impl true
  def handle_info({:settings_saved_redirect}, socket) do
    # Close the settings modal and redirect to refresh the page
    socket =
      socket
      |> assign(:show_settings_modal, false)
      |> redirect(to: ~p"/")

    {:noreply, socket}
  end

  @impl true
  def handle_info({:settings_updated, _updated_settings}, socket) do
    # Settings have been updated, no need to update socket state
    # The components will use the new settings automatically
    {:noreply, socket}
  end

  @impl true
  def handle_info({:factory_reset_completed}, socket) do
    # Factory reset has been completed, update socket state immediately
    socket =
      socket
      |> assign(:show_settings_modal, false)
      |> assign(:connection_sets, [])
      |> assign(:active_connection_set, nil)
      |> stream(:trace_messages, [])
      |> assign(:send_modal_form_state, %{})
      |> assign_page_title(nil)
      |> put_flash(:info, "Factory reset completed successfully")

    {:noreply, socket}
  end

  @impl true
  def handle_info({:connection_sets_updated, updated_connection_sets}, socket) do
    StateManager.handle_connection_sets_updated(socket, updated_connection_sets)
  end

  @impl true
  def handle_info({:ui_state_updated, updated_ui_state}, socket) do
    StateManager.handle_ui_state_updated(socket, updated_ui_state)
  end

  @impl true
  def handle_info({:save_connection_set, params}, socket) do
    # Call the existing handler with the params
    handle_event("save_connection_set", params, socket)
  end

  @impl true
  def handle_info({:update_connection_set, params}, socket) do
    # Call the existing handler with the params
    handle_event("update_connection_set", params, socket)
  end

  @impl true
  def handle_info({:delete_connection_set, params}, socket) do
    # Call the existing handler with the params
    handle_event("delete_connection_set", params, socket)
  end

  @impl true
  def handle_info({:save_variable, params}, socket) do
    # Call the existing handler with the params
    handle_event("save_variable", params, socket)
  end

  @impl true
  def handle_info({:update_variable, params}, socket) do
    # Call the existing handler with the params
    handle_event("update_variable", params, socket)
  end

  @impl true
  def handle_info({:save_connection, connection_params}, socket) do
    # Call the existing handler with the params
    handle_event("save_connection", %{"connection" => connection_params}, socket)
  end

  @impl true
  def handle_info({:save_and_connect_connection, connection_params}, socket) do
    # Call the existing handler with the params
    handle_event("save_and_connect_connection", %{"connection" => connection_params}, socket)
  end

  @impl true
  def handle_info({:update_connection, old_client_id, connection_params}, socket) do
    # Call the existing handler with the params (legacy format)
    handle_event(
      "update_connection",
      %{"connection" => connection_params, "old_client_id" => old_client_id},
      socket
    )
  end

  @impl true
  def handle_info({:update_connection_with_full_params, params}, socket) do
    # Call the existing handler with the complete params (including user properties)
    handle_event("update_connection", params, socket)
  end

  @impl true
  def handle_info({:user_property_changed, params}, socket) do
    # Call the existing handler with the params
    handle_event("user_property_changed", params, socket)
  end

  @impl true
  def handle_info({:add_user_property}, socket) do
    # Call the existing handler
    handle_event("add_user_property", %{}, socket)
  end

  @impl true
  def handle_info({:remove_user_property, params}, socket) do
    # Call the existing handler with the params
    handle_event("remove_user_property", params, socket)
  end

  @impl true
  def handle_info({:mqtt_client_status_changed, broker_name, client_id, status}, socket) do
    ConnectionManager.handle_mqtt_client_status_changed(socket, broker_name, client_id, status)
  end

  @impl true
  def handle_info({:mqtt_client_connection_error, _client_id, _error_message}, socket) do
    # Connection errors are now handled as trace messages instead of flash messages
    # This handler is kept for backward compatibility but no longer shows flash messages
    {:noreply, socket}
  end

  @impl true
  def handle_info({:mqtt_client_connection_time, client_id, timestamp}, socket) do
    # Get the current active connection set
    active_set = socket.assigns.active_connection_set
    Logger.error("Received connection time for client #{client_id} at #{timestamp}")
    Logger.error("Active set: #{inspect(active_set)}")

    if active_set do
      # Find the connection to update
      connections = Map.get(active_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        # Update the connection_time
        updated_connections =
          Enum.map(connections, fn conn ->
            if conn.client_id == client_id do
              Map.put(conn, :connection_time, timestamp)
            else
              conn
            end
          end)

        # Update the active connection set
        updated_set = Map.put(active_set, :connections, updated_connections)

        # Get all connection sets
        connection_sets = socket.assigns.connection_sets

        # Update the connection sets list
        updated_connection_sets =
          Enum.map(connection_sets, fn set ->
            if set.name == active_set.name do
              updated_set
            else
              set
            end
          end)

        # Update the connection sets in the server
        Mqttable.ConnectionSets.update(updated_connection_sets)

        # Update socket assigns
        socket =
          assign(socket,
            active_connection_set: updated_set,
            connection_sets: updated_connection_sets
          )

        {:noreply, socket}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:close_subscription_modal}, socket) do
    socket =
      socket
      |> assign(:show_subscription_modal, false)
      |> assign(:edit_mode, false)
      |> assign(:client_id, nil)
      |> assign(:topic, nil)
      |> assign(:qos, 0)
      |> assign(:nl, false)
      |> assign(:rap, false)
      |> assign(:rh, 0)
      |> assign(:sub_id, nil)
      |> assign(:index, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:delete_subscription, set_name, client_id, topic}, socket) do
    # Reuse the existing unsubscribe logic but without flash messages on success
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, set_name, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Check client connection status
      client_status = Mqttable.MqttClient.Manager.retrieve_client_status(set.name, client_id)

      case client_status do
        :connected ->
          # Client is connected, attempt MQTT unsubscribe
          case Mqttable.MqttClient.Manager.remove_topic_subscription(set.name, client_id, topic) do
            {:ok, _props, _reason_codes} ->
              # Update the connection's topics list without showing success message
              {updated_connection_sets, _success_message} =
                remove_topic_from_connection_state(
                  connection_sets,
                  set_index,
                  connection_index,
                  connection,
                  topic
                )

              # Update the connection sets in the state
              ConnectionSets.update(updated_connection_sets)
              {:noreply, socket}

            {:error, _reason, error_message} ->
              # Show error message only
              socket = put_flash(socket, :error, "Failed to unsubscribe: #{error_message}")
              {:noreply, socket}
          end

        _ ->
          # Client is not connected (:disconnected or :reconnecting)
          # Directly remove topic from state without MQTT unsubscribe
          {updated_connection_sets, _success_message} =
            remove_topic_from_connection_state(
              connection_sets,
              set_index,
              connection_index,
              connection,
              topic
            )

          # Update the connection sets in the state
          ConnectionSets.update(updated_connection_sets)
          {:noreply, socket}
      end
    else
      # Connection not found
      socket = put_flash(socket, :error, "Connection not found")
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info(
        {:subscribe_to_topic, set_name, client_id, topic, sub_opts, sub_id, index},
        socket
      ) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, set_name, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Prepare options for subscription
      opts = [sub_opts: sub_opts]

      # Add subscription identifier if provided
      opts = if sub_id, do: Keyword.put(opts, :id, sub_id), else: opts
      Logger.debug("#{inspect(set.name)} subscribe_to_topic opts: #{inspect(opts)}")

      # Attempt to subscribe to the topic
      case Mqttable.MqttClient.Manager.add_topic_subscription(set.name, client_id, topic, opts) do
        {:ok, {_props, _reason_codes}} ->
          # Update the connection's topics list with topic and options
          # 保持 nl 和 rap 的原始值，不转换为布尔值
          nl_value = Keyword.get(sub_opts, :nl, 0)
          rap_value = Keyword.get(sub_opts, :rap, 0)

          # Get subscription identifier from the opts parameter, not from sub_opts
          sub_id_value = sub_id

          # Create topic entry with all subscription options
          topic_entry = %{
            topic: topic,
            qos: Keyword.get(sub_opts, :qos, 0),
            nl: nl_value,
            rap: rap_value,
            rh: Keyword.get(sub_opts, :rh, 0)
          }

          # Add subscription identifier if provided
          topic_entry =
            if sub_id_value && is_integer(sub_id_value) && sub_id_value > 0 do
              Map.put(topic_entry, :id, sub_id_value)
            else
              topic_entry
            end

          # Get current topics (always using new format - list of maps)
          current_topics = connection.topics || []

          # Check if we're editing an existing topic or adding a new one
          updated_topics =
            if index != nil && index != "" do
              # Try to convert index to integer
              case Integer.parse(index) do
                {idx, _} when idx >= 0 and idx < length(current_topics) ->
                  # Update existing topic at the specified index
                  List.replace_at(current_topics, idx, topic_entry)

                _ ->
                  # Invalid index, add as new topic
                  current_topics ++ [topic_entry]
              end
            else
              # No index provided, check if topic already exists
              case Enum.find_index(current_topics, fn t -> t.topic == topic end) do
                nil ->
                  # Topic doesn't exist, add as new topic
                  current_topics ++ [topic_entry]

                existing_index ->
                  # Topic exists, replace the existing one
                  List.replace_at(current_topics, existing_index, topic_entry)
              end
            end

          updated_connection = Map.put(connection, :topics, updated_topics)

          # Update the connection in the connection set
          updated_connections =
            List.replace_at(set.connections, connection_index, updated_connection)

          updated_set = Map.put(set, :connections, updated_connections)

          # Update the connection set in the connection sets list
          updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

          # Update the connection sets in the state
          ConnectionSets.update(updated_connection_sets)

          # No flash message for successful subscription (similar to schedule message behavior)
          {:noreply, socket}

        {:error, _reason, error_message} ->
          # Show error message
          socket = put_flash(socket, :error, "Failed to subscribe: #{error_message}")
          {:noreply, socket}

        {:error, :not_connected} ->
          # Show error message
          socket = put_flash(socket, :error, "Client is not connected")
          {:noreply, socket}
      end
    else
      # Connection not found
      socket = put_flash(socket, :error, "Connection not found")
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info(
        {:mqtt_client_topic_subscribed, set_name, client_id, topic, opts, sub_id},
        socket
      ) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, set_name, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Check if the topic is already in the list
      current_topics = connection.topics || []

      # Check if the topic exists in the list (always using new format - list of maps)
      topic_index =
        Enum.find_index(current_topics, fn t ->
          case t do
            %{topic: topic_str} -> topic_str == topic
            _ -> false
          end
        end)

      topic_exists = topic_index != nil

      if topic_exists do
        # Topic already exists, no need to update
        {:noreply, socket}
      else
        # Update the connection's topics list with topic and options
        # 保持 nl 和 rap 的原始值，不转换为布尔值
        nl_value = Keyword.get(opts, :nl, 0)
        rap_value = Keyword.get(opts, :rap, 0)

        # Get subscription identifier from the sub_id parameter
        sub_id_value = sub_id

        # Create topic entry with all subscription options
        topic_entry = %{
          topic: topic,
          qos: Keyword.get(opts, :qos, 0),
          nl: nl_value,
          rap: rap_value,
          rh: Keyword.get(opts, :rh, 0)
        }

        # Add subscription identifier if provided
        topic_entry =
          if sub_id_value && is_integer(sub_id_value) && sub_id_value > 0 do
            Map.put(topic_entry, :id, sub_id_value)
          else
            topic_entry
          end

        # Get current topics (always using new format - list of maps)
        current_topics = connection.topics || []

        # Add new topic
        updated_topics = current_topics ++ [topic_entry]

        updated_connection = Map.put(connection, :topics, updated_topics)

        # Update the connection in the connection set
        updated_connections =
          List.replace_at(set.connections, connection_index, updated_connection)

        updated_set = Map.put(set, :connections, updated_connections)

        # Update the connection set in the connection sets list
        updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

        # Update the connection sets in the state
        ConnectionSets.update(updated_connection_sets)

        # Update socket assigns
        socket =
          assign(socket,
            active_connection_set: updated_set,
            connection_sets: updated_connection_sets
          )

        {:noreply, socket}
      end
    else
      # Connection not found
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_client_topic_unsubscribed, broker_name, client_id, topic}, socket) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} =
      find_connection_indices(connection_sets, broker_name, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Update the connection's topics list
      current_topics = connection.topics || []

      # Remove the topic from the list (always using new format - list of maps)
      updated_topics =
        Enum.reject(current_topics, fn t ->
          case t do
            %{topic: topic_str} -> topic_str == topic
            _ -> false
          end
        end)

      updated_connection = Map.put(connection, :topics, updated_topics)

      # Update the connection in the connection set
      updated_connections = List.replace_at(set.connections, connection_index, updated_connection)
      updated_set = Map.put(set, :connections, updated_connections)

      # Update the connection set in the connection sets list
      updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

      # Update the connection sets in the state
      ConnectionSets.update(updated_connection_sets)

      # Update socket assigns
      socket =
        assign(socket,
          active_connection_set: updated_set,
          connection_sets: updated_connection_sets
        )

      {:noreply, socket}
    else
      # Connection not found
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_trace_message, trace_message}, socket) do
    MessageHandler.handle_mqtt_trace_message(socket, trace_message)
  end

  @impl true
  def handle_info({:clear_trace_messages, broker_name}, socket) do
    # Clear the trace_messages state for the specified broker
    # This is called when the TraceGridComponent clears its messages
    active_broker_name =
      if socket.assigns.active_connection_set do
        socket.assigns.active_connection_set.name
      else
        nil
      end

    # Only clear if the broker matches the currently active broker
    if broker_name == active_broker_name do
      {:noreply, stream(socket, :trace_messages, [])}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:message_sent_successfully, packet_info}, socket) do
    {message, flash_type} = format_publish_result(packet_info)

    # Show flash message for all cases including successful sends
    socket =
      case flash_type do
        :error ->
          put_flash(socket, :error, message)

        :warning ->
          put_flash(socket, :warning, message)

        :info ->
          # Success case - show success flash message with auto-dismiss
          socket
          |> put_flash(:info, message)
          |> Phoenix.LiveView.push_event("auto_dismiss_flash", %{kind: "info", delay: 5000})
      end

    {:noreply, socket}
  end

  @impl true
  def handle_info({:message_send_error, error_message}, socket) do
    socket = put_flash(socket, :error, error_message)
    {:noreply, socket}
  end

  @impl true
  def handle_info(
        {:mqtt_client_subscription_failed, broker_name, client_id, topic, error_message},
        socket
      ) do
    # Show flash message for subscription failure
    flash_message =
      "Failed to resubscribe to topic '#{topic}' for client '#{client_id}': #{error_message}"

    socket = put_flash(socket, :error, flash_message)

    # Remove the failed subscription from connection state
    connection_sets = socket.assigns.connection_sets

    {set_index, connection_index} =
      find_connection_indices(connection_sets, broker_name, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Remove the topic from connection state
      {updated_connection_sets, _success_message} =
        remove_topic_from_connection_state(
          connection_sets,
          set_index,
          connection_index,
          connection,
          topic
        )

      # Update the connection sets
      ConnectionSets.update(updated_connection_sets)

      # Update socket assigns
      socket =
        assign(socket,
          connection_sets: updated_connection_sets,
          active_connection_set:
            if socket.assigns.active_connection_set &&
                 socket.assigns.active_connection_set.name ==
                   Enum.at(updated_connection_sets, set_index).name do
              Enum.at(updated_connection_sets, set_index)
            else
              socket.assigns.active_connection_set
            end
        )

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:update_send_modal_form, form_state}, socket) do
    # Save form state to ui_state for the current broker
    active_connection_set = socket.assigns[:active_connection_set]
    broker_name = if active_connection_set, do: active_connection_set.name, else: nil

    if broker_name do
      Mqttable.ConnectionSets.update_send_modal_form_state(broker_name, form_state)
    end

    # Only update socket if form state actually changed to prevent unnecessary re-renders
    current_form_state = socket.assigns[:send_modal_form_state]

    if current_form_state != form_state do
      {:noreply, assign(socket, :send_modal_form_state, form_state)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:update_send_modal_form, form_state, broker_name}, socket) do
    # Save form state to ui_state for the specified broker
    if broker_name do
      Mqttable.ConnectionSets.update_send_modal_form_state(broker_name, form_state)
    end

    # Only update socket if form state actually changed to prevent unnecessary re-renders
    current_form_state = socket.assigns[:send_modal_form_state]

    if current_form_state != form_state do
      {:noreply, assign(socket, :send_modal_form_state, form_state)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:open_detail_modal, message}, socket) do
    socket =
      socket
      |> assign(:show_detail_modal, true)
      |> assign(:detail_modal_message, message)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:send_message_direct, form_state}, socket) do
    # Get the active broker name from the current socket
    active_connection_set = socket.assigns[:active_connection_set]
    active_broker_name = if active_connection_set, do: active_connection_set.name, else: nil

    # Send message to the SendMessageModalComponent to trigger send action
    send_update(MqttableWeb.SendMessageModalComponent,
      id: "send-message-modal",
      action: :send_message_direct,
      form_state: form_state,
      active_broker_name: active_broker_name
    )

    {:noreply, socket}
  end

  @impl true
  def handle_info({:send_message_direct_trigger, params}, socket) do
    # Handle the direct send trigger from the component
    # Forward to the SendMessageModalComponent
    send_update(MqttableWeb.SendMessageModalComponent,
      id: "send-message-modal",
      action: :send_message_trigger,
      params: params
    )

    {:noreply, socket}
  end

  @impl true
  def handle_info({:close_scheduled_message_modal}, socket) do
    socket =
      socket
      |> assign(:show_scheduled_message_modal, false)
      |> assign(:pre_selected_client_id, nil)
      |> assign(:scheduled_message_edit_mode, false)
      |> assign(:scheduled_message_edit_index, nil)
      |> assign(:scheduled_message_data, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:add_scheduled_message, set_name, client_id, scheduled_message}, socket) do
    ScheduledMessageManager.handle_add_scheduled_message(
      socket,
      set_name,
      client_id,
      scheduled_message
    )
  end

  @impl true
  def handle_info(
        {:update_scheduled_message, set_name, client_id, index, scheduled_message},
        socket
      ) do
    ScheduledMessageManager.handle_update_scheduled_message(
      socket,
      set_name,
      client_id,
      index,
      scheduled_message
    )
  end

  @impl true
  def handle_info({:push_grid_data_update, data}, socket) do
    # Push grid data update event to the TraceSlickGrid JavaScript Hook
    # Use push_event with 3 parameters and target the specific element
    socket = Phoenix.LiveView.push_event(socket, "grid_data_update", data)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:push_export_event, data}, socket) do
    # Push export event to the TraceSlickGrid JavaScript Hook
    socket = Phoenix.LiveView.push_event(socket, "export_data", data)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:broker_tabs_collapsed_changed, _collapsed}, socket) do
    # No need to store state in socket, CSS will read from expanded_sets directly
    # Push event to JavaScript to trigger height recalculation
    socket = Phoenix.LiveView.push_event(socket, "broker_tabs_collapsed_changed", %{})
    {:noreply, socket}
  end

  @impl true
  def handle_info(:sync_connection_states, socket) do
    # Sync connection states for all clients to catch any missed connection time broadcasts
    # This is called after LiveView mounts and subscribes to PubSub
    sync_all_connection_states(socket.assigns.connection_sets)
    {:noreply, socket}
  end

  # Template manager event handlers removed - using simplified payload editor now

  @impl true
  def handle_info({:payload_changed, _payload}, socket) do
    # Handle payload changes from UnifiedPayloadEditorComponent
    # This is typically called when a file is removed or payload is cleared
    # For now, we just acknowledge the message without taking action
    # The component handles its own state updates
    {:noreply, socket}
  end

  @impl true
  def handle_info({:payload_editor_changed, payload, payload_format}, socket) do
    # Handle payload editor changes from PayloadEditorComponent
    # Update the send modal form state
    current_form_state = socket.assigns[:send_modal_form_state] || %{}

    # Update the format-specific payload (no longer using main "payload" field)
    updated_form_state =
      current_form_state
      |> Map.put("payload_format", payload_format)
      |> update_format_specific_payload_in_form(payload_format, payload)

    # Save form state to ui_state for the current broker
    active_connection_set = socket.assigns[:active_connection_set]
    broker_name = if active_connection_set, do: active_connection_set.name, else: nil

    if broker_name do
      Mqttable.ConnectionSets.update_send_modal_form_state(broker_name, updated_form_state)
    end

    {:noreply, assign(socket, :send_modal_form_state, updated_form_state)}
  end

  # Serialize message for JSON encoding to avoid Protocol.UndefinedError with tuples
  defp serialize_message_for_json(message) when is_map(message) do
    message
    |> Map.update(:topics, [], &serialize_topics_for_json/1)
    |> Map.update(:properties, %{}, &serialize_properties_for_json/1)
    |> ensure_json_serializable()
  end

  defp serialize_message_for_json(message), do: message

  defp serialize_topics_for_json(topics) when is_list(topics) do
    Enum.map(topics, fn
      {topic, opts} when is_binary(topic) and is_map(opts) ->
        %{topic: topic, options: opts}

      topic when is_binary(topic) ->
        %{topic: topic, options: %{}}

      other ->
        %{topic: to_string(other), options: %{}}
    end)
  end

  defp serialize_topics_for_json(topics), do: topics

  defp serialize_properties_for_json(properties) when is_map(properties) do
    # Ensure all property values are JSON-serializable
    Enum.into(properties, %{}, fn {key, value} ->
      {key, ensure_json_value(value)}
    end)
  end

  defp serialize_properties_for_json(properties), do: properties

  defp ensure_json_serializable(data) when is_map(data) do
    Enum.into(data, %{}, fn {key, value} ->
      {key, ensure_json_value(value)}
    end)
  end

  defp ensure_json_serializable(data) when is_list(data) do
    Enum.map(data, &ensure_json_value/1)
  end

  defp ensure_json_serializable(data), do: ensure_json_value(data)

  defp ensure_json_value(value) when is_binary(value) do
    if String.valid?(value) do
      value
    else
      # Encode non-UTF-8 binary data as base64 for safe JSON serialization
      Base.encode64(value)
    end
  end

  defp ensure_json_value(value) when is_number(value) or is_boolean(value) or is_nil(value),
    do: value

  defp ensure_json_value(value) when is_atom(value), do: to_string(value)
  defp ensure_json_value(value) when is_list(value), do: Enum.map(value, &ensure_json_value/1)
  defp ensure_json_value(value) when is_struct(value), do: inspect(value)
  defp ensure_json_value(value) when is_map(value), do: ensure_json_serializable(value)

  defp ensure_json_value(value) when is_tuple(value),
    do: Tuple.to_list(value) |> ensure_json_value()

  defp ensure_json_value(value) when is_port(value), do: inspect(value)
  defp ensure_json_value(value) when is_pid(value), do: inspect(value)
  defp ensure_json_value(value) when is_reference(value), do: inspect(value)

  defp ensure_json_value(value), do: to_string(value)

  # Helper function to load send modal form state from ui_state
  defp load_send_modal_form_state(active_connection_set) do
    if active_connection_set do
      broker_name = active_connection_set.name
      stored_form_state = Mqttable.ConnectionSets.get_send_modal_form_state(broker_name)

      if stored_form_state do
        # Ensure format-specific payloads exist in stored state
        ensure_format_specific_payloads_in_form(stored_form_state)
      else
        default_publish_form()
      end
    else
      default_publish_form()
    end
  end

  defp find_connection_indices(connection_sets, set_name, client_id) do
    set_index =
      Enum.find_index(connection_sets, fn set -> set.name == set_name end)

    if set_index do
      set = Enum.at(connection_sets, set_index)

      connection_index =
        Enum.find_index(set.connections, fn conn -> conn.client_id == client_id end)

      {set_index, connection_index}
    else
      {nil, nil}
    end
  end

  @spec remove_topic_from_connection_state(
          connection_sets(),
          non_neg_integer(),
          non_neg_integer(),
          connection(),
          String.t()
        ) :: {connection_sets(), String.t()}
  defp remove_topic_from_connection_state(
         connection_sets,
         set_index,
         connection_index,
         connection,
         topic
       ) do
    current_topics = connection.topics || []

    updated_topics =
      Enum.reject(current_topics, fn t ->
        case t do
          %{topic: topic_str} -> topic_str == topic
          _ -> false
        end
      end)

    updated_connection = Map.put(connection, :topics, updated_topics)
    set = Enum.at(connection_sets, set_index)
    updated_connections = List.replace_at(set.connections, connection_index, updated_connection)
    updated_set = Map.put(set, :connections, updated_connections)
    updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

    success_message = "Unsubscribed from topic: #{topic}"
    {updated_connection_sets, success_message}
  end

  # Private function to handle broker tab closing
  defp handle_close_broker_tab(socket, name) do
    # Find the broker to close
    broker_to_close =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, name)

    if broker_to_close do
      # Disconnect all connections in this broker before closing
      if Map.get(broker_to_close, :connections) do
        Enum.each(broker_to_close.connections, fn connection ->
          if connection.client_id do
            MqttClientManager.terminate_client_connection(name, connection.client_id)
          end
        end)
      end

      # Remove the trace table for this broker
      Mqttable.TraceManager.remove_broker(name)

      # Clean all UI state data related to this broker
      ConnectionSets.clean_broker_ui_state(name)

      # Remove the broker from the list
      updated_connection_sets =
        Enum.reject(socket.assigns.connection_sets, fn set ->
          set.name == name
        end)

      # Update the connection sets
      ConnectionSets.update(updated_connection_sets)

      # Select appropriate active broker using our helper function
      current_active_name =
        if socket.assigns.active_connection_set,
          do: socket.assigns.active_connection_set.name,
          else: nil

      new_active_set =
        if current_active_name == name do
          # Deleted broker was active, select a replacement
          ConnectionHelpers.select_active_broker(updated_connection_sets)
        else
          # Keep current active broker if it still exists
          ConnectionHelpers.select_active_broker(updated_connection_sets, current_active_name)
        end

      # Update UI state with new active broker
      new_active_name = if new_active_set, do: new_active_set.name, else: nil
      ConnectionSets.update_active_connection_set(new_active_name)

      # Load trace messages for the new active broker
      trace_messages =
        if new_active_name do
          Mqttable.TraceManager.get_messages(new_active_name)
        else
          []
        end

      socket =
        socket
        |> assign(:connection_sets, updated_connection_sets)
        |> assign(:active_connection_set, new_active_set)
        |> stream(:trace_messages, trace_messages)
        |> assign_page_title(new_active_set)
        |> put_flash(:info, "Broker '#{name}' closed successfully")

      {:noreply, socket}
    else
      {:noreply, put_flash(socket, :error, "Broker not found")}
    end
  end

  # Default form state for the send message modal
  defp default_publish_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload_format" => "text",
      "qos" => 0,
      "retain" => false,
      "content_type" => "",
      "payload_format_indicator" => false,
      "message_expiry_interval" => 0,
      "topic_alias" => 0,
      "response_topic" => "",
      "correlation_data" => "",
      "user_properties" => [],
      # Format-specific payloads
      "payload_text" => "",
      "payload_json" => "",
      "payload_hex" => "",
      "payload_file" => ""
    }
  end

  # Format publish result based on the type of response
  defp format_publish_result(packet_info) do
    case packet_info do
      # QoS 0 messages return simple integer packet_id (usually 0)
      packet_id when is_integer(packet_id) ->
        {"Message sent successfully (Packet ID: #{packet_id})", :info}

      # QoS 1/2 messages return a map with detailed information
      %{packet_id: packet_id, reason_code: reason_code, reason_code_name: reason_name} ->
        case reason_code do
          0 ->
            # Success
            {"Message sent successfully (Packet ID: #{packet_id})", :info}

          # Error codes that indicate failure
          code
          when code in [
                 16,
                 17,
                 18,
                 19,
                 128,
                 129,
                 130,
                 131,
                 132,
                 133,
                 134,
                 135,
                 136,
                 137,
                 138,
                 139,
                 140,
                 141,
                 142,
                 143
               ] ->
            # 16: No matching subscribers
            # 17: No subscription existed
            # 18: Unspecified error
            # 19: Implementation specific error
            # 128+: Protocol errors, quota exceeded, etc.
            reason_text = format_reason_code_name(reason_name)
            {"Message delivery failed: #{reason_text} (Code: #{code})", :error}

          # Warning codes (informational but not necessarily errors)
          code when code in [1, 2, 3, 4] ->
            # Informational codes
            reason_text = format_reason_code_name(reason_name)
            {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

          code ->
            # Other unknown codes
            reason_text = format_reason_code_name(reason_name)
            {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
        end

      # Fallback for unexpected format
      other ->
        {"Message sent (Response: #{inspect(other)})", :info}
    end
  end

  # Format reason code name for display
  defp format_reason_code_name(reason_name) when is_atom(reason_name) do
    reason_name
    |> Atom.to_string()
    |> String.replace("_", " ")
    |> String.capitalize()
  end

  defp format_reason_code_name(other), do: inspect(other)

  @spec assign_page_title(socket(), connection_set() | nil) :: socket()
  defp assign_page_title(socket, active_connection_set) do
    page_title =
      if active_connection_set do
        "#{active_connection_set.name} - Mqttable"
      else
        "Mqttable"
      end

    assign(socket, :page_title, page_title)
  end

  @spec update_format_specific_payload_in_form(map(), String.t(), String.t()) :: map()
  defp update_format_specific_payload_in_form(form_state, format, payload) do
    case format do
      "text" -> Map.put(form_state, "payload_text", payload)
      "json" -> Map.put(form_state, "payload_json", payload)
      "hex" -> Map.put(form_state, "payload_hex", payload)
      "file" -> Map.put(form_state, "payload_file", payload)
      _ -> Map.put(form_state, "payload_text", payload)
    end
  end

  @spec ensure_format_specific_payloads_in_form(map()) :: map()
  defp ensure_format_specific_payloads_in_form(form_state) do
    form_state
    |> Map.put_new("payload_text", "")
    |> Map.put_new("payload_json", "")
    |> Map.put_new("payload_hex", "")
    |> Map.put_new("payload_file", "")
  end
end
